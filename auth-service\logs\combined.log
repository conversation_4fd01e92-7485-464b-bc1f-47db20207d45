{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-20 08:21:07"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:21:07"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:22:11","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:22:11","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:22:32","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:22:32","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:22:32","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:22:32","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:25:23","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:25:23","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:25:23","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:25:23","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}

{"level":"info","maxRecords":10000,"message":"UsageTracker initialized","retentionDays":30,"service":"analysis-worker","timestamp":"2025-07-20 08:19:26","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-20 08:19:26","version":"1.0.0","workerConcurrency":"10"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-20 08:19:26","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 08:19:26","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-20 08:19:26","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 08:19:26","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-20 08:19:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:19:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:20:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:20:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:21:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:21:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:22:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 08:22:32","userEmail":"<EMAIL>","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-20 08:22:32","userEmail":"<EMAIL>","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Updating analysis job status","service":"analysis-worker","status":"processing","timestamp":"2025-07-20 08:22:32","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Analysis job status updated successfully","service":"analysis-worker","status":"processing","timestamp":"2025-07-20 08:22:32","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-20 08:22:32","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-20 08:22:32","useMockModel":true,"version":"1.0.0"}
{"inputTokens":1223,"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Mock token counting completed","mock":true,"service":"analysis-worker","timestamp":"2025-07-20 08:22:32","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-20 08:22:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:22:56","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-20 08:23:02","version":"1.0.0","weaknessesCount":3}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-20 08:23:02","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-20 08:23:02","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Analysis result saved successfully","resultId":"8c110d96-827c-481a-b5fb-05ddb73e2f75","service":"analysis-worker","status":201,"timestamp":"2025-07-20 08:23:04","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Analysis result saved to Archive Service","resultId":"8c110d96-827c-481a-b5fb-05ddb73e2f75","service":"analysis-worker","timestamp":"2025-07-20 08:23:04","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Updating analysis job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 08:23:04","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Analysis job status updated successfully","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 08:23:04","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Assessment job status updated","resultId":"8c110d96-827c-481a-b5fb-05ddb73e2f75","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 08:23:04","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Assessment job status updated","resultId":"8c110d96-827c-481a-b5fb-05ddb73e2f75","service":"analysis-worker","timestamp":"2025-07-20 08:23:04","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"error":"","jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 08:23:04","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Analysis completion notification sent","resultId":"8c110d96-827c-481a-b5fb-05ddb73e2f75","service":"analysis-worker","timestamp":"2025-07-20 08:23:04","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"info","message":"Assessment job processed successfully","processingTime":"32260ms","resultId":"8c110d96-827c-481a-b5fb-05ddb73e2f75","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 08:23:04","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:23:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:23:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:24:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:24:56","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 08:25:23","userEmail":"<EMAIL>","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-20 08:25:23","userEmail":"<EMAIL>","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Updating analysis job status","service":"analysis-worker","status":"processing","timestamp":"2025-07-20 08:25:23","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Analysis job status updated successfully","service":"analysis-worker","status":"processing","timestamp":"2025-07-20 08:25:23","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-20 08:25:23","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-20 08:25:23","useMockModel":true,"version":"1.0.0"}
{"inputTokens":1174,"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Mock token counting completed","mock":true,"service":"analysis-worker","timestamp":"2025-07-20 08:25:23","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-20 08:25:23","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:25:26","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-20 08:25:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-20 08:25:53","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-20 08:25:53","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Analysis result saved successfully","resultId":"1e80bd86-bf48-486a-ad83-fa12fd86c4f5","service":"analysis-worker","status":201,"timestamp":"2025-07-20 08:25:55","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Analysis result saved to Archive Service","resultId":"1e80bd86-bf48-486a-ad83-fa12fd86c4f5","service":"analysis-worker","timestamp":"2025-07-20 08:25:55","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Updating analysis job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 08:25:55","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Analysis job status updated successfully","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 08:25:55","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Assessment job status updated","resultId":"1e80bd86-bf48-486a-ad83-fa12fd86c4f5","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 08:25:55","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Assessment job status updated","resultId":"1e80bd86-bf48-486a-ad83-fa12fd86c4f5","service":"analysis-worker","timestamp":"2025-07-20 08:25:55","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"error":"","jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 08:25:55","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Analysis completion notification sent","resultId":"1e80bd86-bf48-486a-ad83-fa12fd86c4f5","service":"analysis-worker","timestamp":"2025-07-20 08:25:55","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"info","message":"Assessment job processed successfully","processingTime":"32168ms","resultId":"1e80bd86-bf48-486a-ad83-fa12fd86c4f5","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 08:25:55","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 08:25:56","version":"1.0.0"}
